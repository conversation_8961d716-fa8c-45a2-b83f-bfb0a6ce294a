from contextlib import asynccontextmanager
import logging
import logging.config

import anyio
import anyio.to_thread
from fastapi import FastAP<PERSON>
from fastapi.responses import JSONResponse, PlainTextResponse, Response
from fastapi.middleware.cors import CORSMiddleware
from knifes.exception_handlers import install_exception_handlers
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST


from pkg.exceptions import ExtractError
from router import extract
from pkg.config import LOGGING_CONFIG, settings


# configure logging
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_: FastAPI):
    logger.info("startup...")
    limiter = anyio.to_thread.current_default_thread_limiter()
    limiter.total_tokens = 9999  # change the number of threads available
    yield
    logger.info("shutdown...")


app = FastAPI(lifespan=lifespan, docs_url="/docs" if settings.node_name == "local" else None, redoc_url=None)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # stream interface needs to be accessed by other domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(extract.router)


@app.get("/metrics")
async def metrics():
    """prometheus metrics endpoint"""
    return Response(content=generate_latest(), headers={"Content-Type": CONTENT_TYPE_LATEST})


# install exception handlers to handle HTTPException and RequestValidationError
install_exception_handlers(app)


@app.exception_handler(ExtractError)
async def extract_error_handler(_request, exc: ExtractError):
    return JSONResponse(
        status_code=400,
        content={
            "message": exc.error_enum.message,
            "code": exc.error_enum.code,
            "detail": exc.detail,
        },
    )


@app.get("/")
async def root():
    logger.info(f"hello, {settings.node_name}")
    return f"hello, {settings.node_name}"


@app.get("/robots.txt", response_class=PlainTextResponse)
async def robots():
    return PlainTextResponse(content="User-agent: *\nDisallow: /")
