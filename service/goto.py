import json
from datetime import datetime, timedelta
from dataclasses import dataclass, field

from knifes import aes

from pkg.config import settings
from pkg.jsonx import stringify
from pkg.sites import NODE_DICT


def cal_expire_time(expire_hours: int) -> float:
    return (datetime.now() + timedelta(hours=expire_hours)).timestamp()


@dataclass(kw_only=True)
class Payload:
    expire_time: float = field(default_factory=lambda: cal_expire_time(12))  # default 12 hours
    type: str = field(init=False)

    def __post_init__(self):
        self.type = self.__class__.__name__


@dataclass(kw_only=True)
class SinglePostPayload(Payload):
    url: str


@dataclass(kw_only=True)
class BilibiliBangumiPayload(Payload):
    bvid: str
    cid: str
    aid: str
    ep_id: str


@dataclass(kw_only=True)
class BilibiliAcgPayload(Payload):
    bvid: str
    cid: str


@dataclass(kw_only=True)
class TwitterScreenshotPayload(Payload):
    text: str | None
    created_at: str
    username: str | None = None
    fullname: str | None = None
    verified: bool | None = None
    profile_image_url: str | None = None
    expire_time: float = field(default_factory=lambda: cal_expire_time(1))  # default 1 hours


@dataclass(kw_only=True)
class StreamPayload(Payload):
    url: str
    headers: dict | None = None
    expire_time: float = field(default_factory=lambda: cal_expire_time(1))  # default 1 hours


def generate_stream_url(data: str | Payload | None, cdn=True):
    if not data:
        return None
    if not isinstance(data, Payload):
        data = StreamPayload(url=data)
    node_base_url = "https://garden-us-cdn.aipark.top" if cdn else NODE_DICT[settings.node_name].external_base_url
    return f"{node_base_url}/stream?payload={aes.encrypt(stringify(data), settings.aes_key)}"


def decode_payload(payload: str) -> dict:
    return json.loads(aes.decrypt(payload, settings.aes_key))
