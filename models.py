from dataclasses import dataclass, field

from knifes.media import MediaType, get_quality_note_by_quality
from knifes.urls import sanitize_url
from pydantic import BaseModel, computed_field


class ExtractParams(BaseModel):
    url: str
    extractor: str
    cursor: str | float | int | None = None
    request_from: str = "unknown"
    backward_compatibility: bool = False


@dataclass
class UserInfo:
    username: str | None = None
    avatar: str | None = None


@dataclass
class StatsInfo:
    comment_count: int | None = None
    digg_count: int | None = None
    play_count: int | None = None
    share_count: int | None = None


class MediaFormat(BaseModel):
    quality: int  # media quality, such as video resolution,  e.g. 720、1080、2160、4320、...
    video_url: str | None
    video_ext: str | None = None
    video_size: int | None = None
    audio_url: str | None = None
    audio_ext: str | None = None
    audio_size: int | None = None
    separate: int = 0

    @computed_field
    def quality_note(self) -> str:
        return get_quality_note_by_quality(self.quality)

    @classmethod
    def audio(cls, audio_url, audio_ext, audio_size=0):
        return MediaFormat(
            quality=0,
            video_url=None,
            audio_url=audio_url,
            audio_ext=audio_ext,
            audio_size=audio_size,
        )


class MediaInfo:
    preview_proxy_url: str | None = None  # 封面图(代理地址)
    formats: list[MediaFormat] | None = None
    headers: dict[str, str] | None = None  # 请求头

    def __init__(self, media_type: MediaType, resource_url, preview_url=None):
        self.media_type = media_type  # image、video、audio
        self.resource_url = sanitize_url(resource_url)  # 资源下载地址
        self.preview_url = sanitize_url(preview_url)  # 封面图(可能无)

    @classmethod
    def image(cls, image_url):
        return MediaInfo(media_type=MediaType.IMAGE, resource_url=image_url)

    @classmethod
    def video(cls, video_url, cover_url=None):
        return MediaInfo(media_type=MediaType.VIDEO, resource_url=video_url, preview_url=cover_url)

    @classmethod
    def audio(cls, audio_url, cover_url=None):
        return MediaInfo(media_type=MediaType.AUDIO, resource_url=audio_url, preview_url=cover_url)


@dataclass
class PostInfo:
    id: str | None = None
    create_time: str | int | None = None
    text: str | None = None
    medias: list[MediaInfo] = field(default_factory=list)
    stats: StatsInfo | None = None


@dataclass
class UserPostsResult:
    next_cursor: str = "no_more"
    has_more: bool = False
    posts: list[PostInfo] = field(default_factory=list)
    user: UserInfo | None = None
    overseas: int = 0


@dataclass
class PostResult:
    text: str | None = None
    medias: list[MediaInfo] = field(default_factory=list)
    stats: StatsInfo | None = None
    overseas: int = 0

    def is_empty(self):
        return not self.medias

    @classmethod
    def single_media(cls, media: MediaInfo, text=None):
        return PostResult(text=text, medias=[media])



