#!/usr/bin/env python3
"""
简单测试Bilibili提取器的基本功能
"""
import re

def _is_bilibili_user_url(url: str) -> bool:
    """检查是否是Bilibili用户主页URL"""
    patterns = [
        r'https?://space\.bilibili\.com/\d+(?:/video)?/?$',
        r'https?://www\.bilibili\.com/space/\d+(?:/video)?/?$',
    ]
    return any(re.match(pattern, url) for pattern in patterns)

def _extract_user_id(url: str) -> str:
    """从URL中提取用户ID"""
    patterns = [
        r'space\.bilibili\.com/(\d+)',
        r'bilibili\.com/space/(\d+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return ""

def test_basic_functions():
    """测试基本功能"""
    print("=== 测试URL检测功能 ===")
    test_urls = [
        "https://space.bilibili.com/123456",
        "https://www.bilibili.com/space/789012",
        "https://space.bilibili.com/123456/video",
        "https://space.bilibili.com/123456/video/",
        "https://www.bilibili.com/video/BV1234567890",
        "https://space.bilibili.com/123456/dynamic",  # 应该返回False
    ]
    
    for url in test_urls:
        is_user_url = _is_bilibili_user_url(url)
        user_id = _extract_user_id(url)
        print(f"URL: {url}")
        print(f"  是用户主页: {is_user_url}")
        print(f"  用户ID: {user_id}")
        print()

if __name__ == "__main__":
    test_basic_functions()
