# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: garden
#   annotations:
#     cert-manager.io/cluster-issuer: letsencrypt
# spec:
#   ingressClassName: nginx
#   rules:
#   - host: garden-hk.smilelikeyou.com
#     http:
#       paths:
#       - path: /
#         pathType: Prefix
#         backend:
#           service:
#             name: garden
#             port:
#               number: 80
#   tls:
#     - hosts:
#       - garden-hk.smilelikeyou.com
#       secretName: garden-hk-smilelikeyou-com-cert
