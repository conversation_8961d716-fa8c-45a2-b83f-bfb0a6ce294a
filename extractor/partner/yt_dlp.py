import logging
from asyncer import asyncify
import yt_dlp
from pkg import urlx
from models import ExtractParams, MediaInfo, PostResult
from service.goto import generate_stream_url, StreamPayload
from pkg.exceptions import ExtractError, ExtractErrorEnum
from pkg.sites import SiteEnum, get_site_enum_by_url

logger = logging.getLogger(__name__)
ydl = yt_dlp.YoutubeDL(
    {
        "skip_download": True,
        "quiet": True,
        "no_check_certificate": True,
    }
)

async def main(params: ExtractParams):
    """
    vk: verify useragent when downloading, m3u8
    """
    result = await asyncify(ydl.extract_info)(url=params.url, download=False)
    if not result:
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

    entries = result.get("entries")
    if entries:
        return PostResult(
            text=result.get("title"),
            medias=[MediaInfo(urlx.guess_media_type(entry.get("ext")), entry["url"], entry.get("thumbnail")) for entry in entries],
        )

    video_url = result.get("url")
    if not video_url and (video_urls := result.get("urls")):
        video_url = video_urls.splitlines()[-1]

    if not video_url:
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

    # if vk video, verify useragent when downloading
    if SiteEnum.VK == get_site_enum_by_url(params.url):
        # formats = list(filter(lambda i: i.get("protocol") == "https" and i.get("ext") == "mp4", result["formats"]))
        # formats.sort(key=lambda i: i.get("height") or 0, reverse=True)
        headers = dict(result["http_headers"]) if result.get("http_headers") else None
        video_url = generate_stream_url(StreamPayload(url=video_url, headers=headers), cdn=False)
        # video_url = generate_stream_url(StreamPayload(url=formats[0]["url"], headers=headers), cdn=False)

    return PostResult.single_media(MediaInfo.video(video_url, result.get("thumbnail")), text=result.get("title"))


# def _sync_extract(url):
#     ydl_opts: dict[str, Any] = {
#         "skip_download": True,
#         "quiet": True,
#         "no_check_certificate": True,
#     }
#     ipv6 = ip_util.local_dynamic_ipv6()
#     logger.info(f"use ipv6: {ipv6}")
#     if ipv6:
#         ydl_opts["source_address"] = ipv6
#     with yt_dlp.YoutubeDL(ydl_opts) as yd:
#         return yd.extract_info(url=url, download=False)
