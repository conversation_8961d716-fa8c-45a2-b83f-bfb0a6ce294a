import logging
import re
from asyncer import asyncify
import yt_dlp
from pkg import urlx
from models import ExtractParams, MediaInfo, PostResult, UserPostsResult, PostInfo, UserInfo
from pkg.exceptions import ExtractError, ExtractErrorEnum

logger = logging.getLogger(__name__)

# 配置yt-dlp，支持Bilibili用户空间提取
ydl = yt_dlp.YoutubeDL({
    "skip_download": True,
    "quiet": True,
    "no_check_certificate": True,
    "extract_flat": True,  # 对于播放列表只提取基本信息
    "playlistend": 50,  # 限制每次提取的视频数量
})

async def main(params: ExtractParams):
    """
    Bilibili主页提取功能
    支持用户主页视频列表提取和单个视频提取
    """
    url = params.url

    # 检查是否是Bilibili用户主页URL
    if _is_bilibili_user_url(url):
        # 提取用户主页视频列表
        return await _extract_user_videos(params)
    else:
        # 如果不是主页URL，尝试单个视频提取
        return await _extract_single_video(params)

def _is_bilibili_user_url(url: str) -> bool:
    """检查是否是Bilibili用户主页URL"""
    patterns = [
        r'https?://space\.bilibili\.com/\d+(?:/video)?/?$',
        r'https?://www\.bilibili\.com/space/\d+(?:/video)?/?$',
    ]
    return any(re.match(pattern, url) for pattern in patterns)

async def _extract_single_video(params: ExtractParams) -> PostResult:
    """提取单个Bilibili视频"""
    try:
        result = await asyncify(ydl.extract_info)(url=params.url, download=False)
        if not result:
            raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

        # 处理播放列表情况
        entries = result.get("entries")
        if entries:
            return PostResult(
                text=result.get("title"),
                medias=[MediaInfo(urlx.guess_media_type(entry.get("ext")), entry["url"], entry.get("thumbnail")) for entry in entries],
            )

        # 处理单个视频
        video_url = result.get("url")
        if not video_url and (video_urls := result.get("urls")):
            video_url = video_urls.splitlines()[-1]

        if not video_url:
            raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

        return PostResult.single_media(
            MediaInfo.video(video_url, result.get("thumbnail")),
            text=result.get("title")
        )
    except Exception as e:
        logger.exception(f"Failed to extract single video: {params.url}")
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail=str(e))

async def _extract_user_videos(params: ExtractParams) -> UserPostsResult:
    """提取用户主页视频列表"""
    try:
        # 构建用户视频列表URL
        user_id = _extract_user_id(params.url)
        if not user_id:
            raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail="无法提取用户ID")

        # 确保URL格式正确
        videos_url = f"https://space.bilibili.com/{user_id}/video"

        logger.info(f"Extracting Bilibili user videos from: {videos_url}")

        # 使用yt-dlp提取用户视频列表
        result = await asyncify(ydl.extract_info)(url=videos_url, download=False)

        if not result:
            raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail="yt-dlp返回空结果")

        logger.info(f"yt-dlp result type: {type(result)}")

        # 提取用户信息
        user_info = UserInfo(
            username=result.get("uploader") or result.get("channel") or result.get("title"),
            avatar=result.get("thumbnail")
        )

        # 提取视频列表
        posts = []
        entries = result.get("entries", [])

        logger.info(f"Found {len(entries)} video entries")

        # 处理分页 - 基于cursor参数
        page = 1
        if params.cursor and params.cursor.isdigit():
            page = int(params.cursor)

        # 每页显示20个视频
        page_size = 20
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_entries = entries[start_idx:end_idx]

        # 处理每个视频条目
        for entry in page_entries:
            if not entry:
                continue

            # 构建视频URL
            video_url = entry.get("url") or entry.get("webpage_url")
            if not video_url and entry.get("id"):
                video_url = f"https://www.bilibili.com/video/{entry['id']}"

            post_info = PostInfo(
                id=entry.get("id"),
                text=entry.get("title"),
                create_time=entry.get("upload_date") or entry.get("timestamp"),
                medias=[MediaInfo.video(video_url, entry.get("thumbnail"))] if video_url else []
            )
            posts.append(post_info)

        # 判断是否还有更多数据
        has_more = len(entries) > end_idx
        next_cursor = str(page + 1) if has_more else "no_more"

        return UserPostsResult(
            next_cursor=next_cursor,
            has_more=has_more,
            posts=posts,
            user=user_info
        )

    except Exception as e:
        logger.exception(f"Failed to extract user videos: {params.url}")
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail=str(e))

def _extract_user_id(url: str) -> str:
    """从URL中提取用户ID"""
    patterns = [
        r'space\.bilibili\.com/(\d+)',
        r'bilibili\.com/space/(\d+)',
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    return ""