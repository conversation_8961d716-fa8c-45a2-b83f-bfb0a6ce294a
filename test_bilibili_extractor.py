#!/usr/bin/env python3
"""
测试Bilibili主页提取器
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models import ExtractParams
from extractor.bilibili.yt_dlp import main, _is_bilibili_user_url, _extract_user_id

async def test_bilibili_extractor():
    """测试Bilibili提取器"""
    
    print("=== 测试URL检测功能 ===")
    test_urls = [
        "https://space.bilibili.com/123456",
        "https://www.bilibili.com/space/789012",
        "https://space.bilibili.com/123456/video",
        "https://www.bilibili.com/video/BV1234567890",
    ]
    
    for url in test_urls:
        is_user_url = _is_bilibili_user_url(url)
        user_id = _extract_user_id(url)
        print(f"URL: {url}")
        print(f"  是用户主页: {is_user_url}")
        print(f"  用户ID: {user_id}")
        print()
    
    print("=== 测试实际提取功能 ===")
    # 使用一个真实的Bilibili用户主页进行测试
    test_url = "https://space.bilibili.com/1958703906"  # 靡烟miya的主页
    
    try:
        params = ExtractParams(
            url=test_url,
            extractor="bilibili.yt_dlp",
            cursor=None,
            request_from="test"
        )
        
        print(f"正在提取: {test_url}")
        result = await main(params)
        
        print(f"提取结果类型: {type(result).__name__}")
        
        if hasattr(result, 'user') and result.user:
            print(f"用户名: {result.user.username}")
            print(f"头像: {result.user.avatar}")
        
        if hasattr(result, 'posts'):
            print(f"视频数量: {len(result.posts)}")
            for i, post in enumerate(result.posts[:3]):  # 只显示前3个
                print(f"  视频 {i+1}: {post.text}")
                if post.medias:
                    print(f"    媒体数量: {len(post.medias)}")
        
        if hasattr(result, 'has_more'):
            print(f"是否有更多: {result.has_more}")
            print(f"下一页游标: {result.next_cursor}")
            
    except Exception as e:
        print(f"提取失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_bilibili_extractor())
