import importlib
import logging

from fastapi import APIRouter
from fastapi.encoders import jsonable_encoder
from httpx import TimeoutException
from knifes import useragent

from pkg.sites import SiteEnum, get_site_enum_by_url
from models import ExtractParams, UserPostsResult
from pkg.exceptions import ExtractError, ExtractErrorEnum

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/extract")
async def extract(params: ExtractParams):
    try:
        m = importlib.import_module(f"garden.extractor.{params.extractor}")
        result = await getattr(m, "main")(params)

        # 主页批量
        if isinstance(result, UserPostsResult):
            logger.info(f"[{params.extractor}][{params.request_from}] url:{params.url}, cursor:{params.cursor}, result:{jsonable_encoder(result)}")
            return _convert_playlist_data_type(result)

        # 单个帖子提取
        if not result or result.is_empty():
            raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

        site_enum = get_site_enum_by_url(params.url)
        if site_enum is SiteEnum.WEIBO:
            for m in result.medias:
                m.headers = useragent.ua_headers("")  # 微博视频使用空UA
        elif site_enum is SiteEnum.XIAOHONGSHU:
            for m in result.medias:
                m.headers = {"Referer": "https://www.xiaohongshu.com/"}
        elif site_enum is SiteEnum.DOUYIN:
            for m in result.medias:
                m.headers = useragent.ua_headers("")
        elif site_enum is SiteEnum.KUAISHOU:
            for m in result.medias:
                m.headers = useragent.ua_headers("")

        logger.info(f"[{params.extractor}][{params.request_from}] url:{params.url}, result:{jsonable_encoder(result)}")
        return result
    except ExtractError as e:
        logger.exception(f"[{params.extractor}] url:{params.url}, fail")
        raise e
    except TimeoutException as e:
        logger.exception(f"[{params.extractor}] url:{params.url}, fail")
        raise ExtractError(ExtractErrorEnum.REQUEST_TIMEOUT_ERROR, detail=str(e))
    except Exception as e:
        logger.exception(f"[{params.extractor}] url:{params.url}, fail")
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail=str(e))


# 主页批量数据类型转换
def _convert_playlist_data_type(result: UserPostsResult):
    result.next_cursor = str(result.next_cursor)
    if result.posts:
        for post in result.posts:
            if post.id:
                post.id = str(post.id)
            if post.create_time:
                post.create_time = str(post.create_time)
    return result


